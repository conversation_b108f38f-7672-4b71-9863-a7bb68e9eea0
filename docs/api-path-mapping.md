# ArchScope API路径映射规范

## 修复说明

根据一致性审查报告，我们重新设计了API路径结构以解决路径冲突问题。

## 新的API路径结构

### 1. 服务管理相关API

#### 服务注册管理 (ServiceRegistryController)
- **基础路径**: `/api/v1/services/registry`
- **功能**: 服务注册、更新、注销等管理操作

| HTTP方法 | 路径 | 功能 | 原路径 |
|---------|------|------|--------|
| POST | `/api/v1/services/registry` | 注册服务 | `/api/v1/services` |
| PUT | `/api/v1/services/registry/{serviceId}` | 更新服务信息 | `/api/v1/services/{serviceId}` |
| DELETE | `/api/v1/services/registry/{serviceId}` | 注销服务 | `/api/v1/services/{serviceId}` |

#### 服务发现 (ServiceDiscoveryController)
- **基础路径**: `/api/v1/services/discovery`
- **功能**: 服务查询、发现、推荐等操作

| HTTP方法 | 路径 | 功能 | 原路径 |
|---------|------|------|--------|
| GET | `/api/v1/services/discovery/active` | 获取所有活跃服务 | `/api/v1/services/active` |
| GET | `/api/v1/services/discovery` | 分页查询服务 | `/api/v1/services` |
| GET | `/api/v1/services/discovery/type/{serviceType}` | 根据类型查询服务 | `/api/v1/services/type/{serviceType}` |
| GET | `/api/v1/services/discovery/search/capability` | 根据能力查找服务 | `/api/v1/services/search/capability` |
| GET | `/api/v1/services/discovery/search/maven` | 根据Maven坐标查找服务 | `/api/v1/services/search/maven` |

### 2. 其他API模块

#### 能力管理 (CapabilityController)
- **基础路径**: `/api/v1/capabilities`
- **状态**: 无变更，路径结构合理

#### 项目管理 (ProjectController)
- **基础路径**: `/api/projects`
- **状态**: 无变更，但建议统一为 `/api/v1/projects`

#### 任务管理 (TaskController)
- **基础路径**: `/api/tasks`
- **状态**: 无变更，但建议统一为 `/api/v1/tasks`

#### 文档管理 (DocumentController)
- **基础路径**: `/api/documents`
- **状态**: 无变更，但建议统一为 `/api/v1/documents`

## 版本控制策略

### 当前状态
- 部分API使用 `/api/v1` 前缀
- 部分API使用 `/api` 前缀

### 建议改进
统一所有API使用 `/api/v1` 前缀：

```
建议的统一路径结构:
- 项目管理: /api/v1/projects
- 任务管理: /api/v1/tasks  
- 文档管理: /api/v1/documents
- 服务注册: /api/v1/services/registry
- 服务发现: /api/v1/services/discovery
- 能力管理: /api/v1/capabilities
- 需求管理: /api/v1/requirements
```

## Swagger配置更新

已更新 `SwaggerConfig.java` 中的路径匹配规则：

```java
@Bean
public GroupedOpenApi serviceDiscoveryApi() {
    return GroupedOpenApi.builder()
            .group("service-discovery")
            .displayName("服务发现系统")
            .pathsToMatch("/api/v1/services/discovery/**", "/api/v1/services/registry/**", 
                         "/api/v1/capabilities/**", "/api/v1/requirements/**")
            .build();
}
```

## OpenAPI文档更新

已更新 `service-discovery-openapi.yaml` 中的路径定义：
- `/services` → `/services/registry`
- `/services/{serviceId}` → `/services/registry/{serviceId}`
- `/discovery/active` → `/services/discovery/active`
- `/discovery` → `/services/discovery`

## 影响评估

### 前端影响
需要更新前端API调用路径：
- 服务注册相关调用需要更新为新的 `/api/v1/services/registry` 路径
- 服务发现相关调用需要更新为新的 `/api/v1/services/discovery` 路径

### 测试影响
需要更新相关的集成测试和API测试用例中的路径引用。

### 文档影响
需要更新API文档和使用指南中的路径示例。

## 后续改进建议

1. **统一版本前缀**: 将所有API路径统一使用 `/api/v1` 前缀
2. **路径命名规范**: 制定统一的路径命名规范文档
3. **自动化测试**: 添加API路径一致性的自动化测试
4. **向后兼容**: 考虑为旧路径添加重定向或废弃警告

## 验证清单

- [x] ServiceRegistryController路径更新为 `/api/v1/services/registry`
- [x] ServiceDiscoveryController路径更新为 `/api/v1/services/discovery`
- [x] SwaggerConfig路径匹配规则更新
- [x] OpenAPI文档路径定义更新
- [x] 编译验证通过
- [ ] 集成测试验证
- [ ] 前端API调用更新
- [ ] 其他Controller路径统一化

## 完成状态

✅ **第一阶段完成**: API路径冲突问题已解决
- 解决了ServiceDiscoveryController和ServiceRegistryController的路径冲突
- 更新了相关的Swagger配置和OpenAPI文档
- 编译验证通过

⏳ **待完成**: 
- 统一其他Controller的版本前缀
- 更新前端API调用
- 更新集成测试用例
