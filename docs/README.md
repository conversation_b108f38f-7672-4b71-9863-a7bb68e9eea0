# ArchScope 文档导航

欢迎来到ArchScope项目文档中心。本文档提供了项目所有技术文档的导航和索引。

## 📚 文档分类

### 🏗️ 架构设计
- [架构总览](architecture.md) - 系统整体架构设计
- [前端架构](frontend-architecture.md) - 前端技术架构
- [数据模型](data-model.md) - 数据库设计和实体关系
- [领域模型](domain-model.md) - DDD领域模型设计
- [UI实现指南](architecture/ui-implementation-guide.md) - 界面实现指导
- [UI原型分析](architecture/ui-prototype-analysis.md) - 原型设计分析

### 🔌 API文档
- [API设计规范](api-design.md) - RESTful API设计标准
- [API路径映射](api-path-mapping.md) - API路径规范和映射
- [OpenAPI规范](api-spec.yaml) - 标准API规范文件
- [LLM集成API](llm-integration-api.md) - LLM服务集成接口

### 📋 规范文档
- [字段命名规范](field-naming-standards.md) - 统一字段命名标准
- [枚举定义规范](enum-definitions-audit.md) - 枚举类型定义规范
- [模块规范](module-specs/) - 各层模块设计规范
  - [应用层规范](module-specs/application-layer-spec.md)
  - [领域层规范](module-specs/domain-layer-spec.md)
  - [基础设施层规范](module-specs/infrastructure-layer-spec.md)
  - [接口层规范](module-specs/interfaces-api-layer-spec.md)

### 💻 开发指南
- [前端开发指南](frontend-development.md) - 前端开发最佳实践
- [Git集成配置](git-personal-access-token-config.md) - Git个人访问令牌配置
- [LLM任务集成指南](llm-task-integration-guide.md) - LLM任务集成开发指南
- [提示词管理](prompt-management.md) - 提示词管理系统

### 🚀 部署运维
- [技术栈](technology-stack.md) - 项目技术栈说明
- [技术实施方案](archscope-technical-implementation.md) - 技术实施方案

### 📊 业务文档
- [用户故事](user-story.md) - 产品用户故事
- [常见问题解答](faq.md) - 用户常见问题
- [技术问答](technical-qa.md) - 深度技术问答
- [风险评估](risk.md) - 项目风险分析

### 🔬 研究报告
- [深度研究报告](deep-research-report-archscope-2025-05-03.md) - 项目深度技术研究
- [项目分析优化](project-analysis-task-optimization.md) - 项目分析任务优化

### 🎨 界面原型
- [界面原型](prototype/) - 前端界面设计原型
  - [原型说明](prototype/README.md)
  - [界面原型文件](prototype/*.html)

### 📝 架构决策记录 (ADRs)
- [ADR模板](adrs/adr-template.md) - 架构决策记录模板
- [ADR-001: 后端架构选择](adrs/ADR-001-Backend-Architecture-Choice.md)
- [ADR-002: 后端模块结构](adrs/ADR-002-Backend-Module-Structure.md)
- [ADR-003: MVP认证策略](adrs/ADR-003-MVP-Authentication-Strategy.md)
- [ADR-004: 数据持久化策略](adrs/ADR-004-Data-Persistence-Strategy.md)
- [ADR-005: 异步处理机制](adrs/ADR-005-Asynchronous-Processing-Mechanism.md)
- [ADR-006: 部署环境](adrs/ADR-006-Deployment-Environment.md)
- [ADR-007: Git集成方案](adrs/ADR-007-Git-Integration-Approach.md)
- [ADR-008: LLM服务集成](adrs/ADR-008-LLM-Service-Integration.md)

### 🔍 审查报告
- [一致性审查报告](consistency-audit-report.md) - 文档与代码一致性审查

## 🎯 快速导航

### 新手入门
1. [架构总览](architecture.md) - 了解系统整体设计
2. [技术栈](technology-stack.md) - 了解技术选型
3. [常见问题解答](faq.md) - 快速了解产品
4. [前端开发指南](frontend-development.md) - 开始前端开发

### 开发者指南
1. [API设计规范](api-design.md) - 了解API标准
2. [字段命名规范](field-naming-standards.md) - 遵循命名标准
3. [枚举定义规范](enum-definitions-audit.md) - 了解枚举使用规范
4. [提示词管理](prompt-management.md) - LLM提示词管理
5. [LLM任务集成指南](llm-task-integration-guide.md) - 集成LLM服务

### 架构师指南
1. [架构决策记录](adrs/) - 了解重要架构决策
2. [领域模型](domain-model.md) - 理解业务领域设计
3. [组件交互](component-interaction.md) - 了解系统交互关系
4. [一致性审查报告](consistency-audit-report.md) - 了解系统一致性状态

## 📝 文档维护

### 文档更新原则
1. **及时更新**: 代码变更时同步更新相关文档
2. **保持一致**: 确保文档与实际实现保持一致
3. **版本控制**: 重要变更记录在CHANGELOG中
4. **审查机制**: 文档变更需要经过审查

### 贡献指南
1. 遵循Markdown格式规范
2. 使用连字符命名文件（kebab-case）
3. 添加适当的文档分类和标签
4. 更新本导航文件的相关链接

### 文档规范
- **命名**: 使用连字符命名（kebab-case）
- **格式**: 遵循Markdown最佳实践
- **结构**: 保持清晰的标题层次
- **链接**: 使用相对路径链接

## 📞 联系方式

如有文档相关问题，请：
1. 提交Issue到项目仓库
2. 联系项目维护团队
3. 参与文档改进讨论

---

**最后更新**: 2025-08-04
**维护者**: ArchScope开发团队
