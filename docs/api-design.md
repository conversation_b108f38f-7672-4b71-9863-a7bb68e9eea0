# ArchScope API 设计规范

## 基础约定

### 根路径 (Base Path)
`/api/v1`

### 数据格式 (Data Format)
所有请求体均为 `application/json` (UTF-8编码)

### 认证 (Authentication)
MVP阶段，面向最终用户的API端点无需认证。

### 统一响应格式

#### 成功响应
```json
{
  "success": true,
  "code": "000000",
  "message": "操作成功",
  "data": {
    // 实际业务数据 DTO
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "code": "XXXXXX",
  "message": "具体的错误描述信息",
  "data": null
}
```

*HTTP状态码依然会正确设置 (如 200, 201, 400, 404, 500等)，响应体遵循此结构。*

## API 端点设计

### 1. 项目管理 API

#### 1.1 提交项目进行分析
```http
POST /api/v1/projects
```

**请求体**:
```json
{
  "repositoryUrl": "https://github.com/user/repo.git",
  "description": "项目描述 (可选)"
}
```

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "项目提交成功",
  "data": {
    "projectId": 12345,
    "repositoryUrl": "https://github.com/user/repo.git",
    "status": "PENDING_ANALYSIS",
    "visibility": "PRIVATE",
    "submittedAt": "2023-11-01T10:00:00Z"
  }
}
```

#### 1.2 获取项目详情
```http
GET /api/v1/projects/{projectId}
```

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "获取成功",
  "data": {
    "projectId": 12345,
    "name": "示例项目",
    "repositoryUrl": "https://github.com/user/repo.git",
    "description": "项目描述",
    "status": "AVAILABLE",
    "visibility": "PUBLIC",
    "projectType": "JAVA",
    "linesOfCode": 10000,
    "fileCount": 150,
    "contributorCount": 5,
    "createdAt": "2023-11-01T10:00:00Z",
    "lastAnalyzedAt": "2023-11-01T12:00:00Z"
  }
}
```

#### 1.3 获取公开项目列表
```http
GET /api/v1/projects?status=AVAILABLE&visibility=PUBLIC&page=1&size=20
```

**查询参数**:
- `status`: 项目状态过滤 (可选)
- `visibility`: 可见性过滤 (可选)
- `projectType`: 项目类型过滤 (可选)
- `page`: 页码 (默认1)
- `size`: 每页大小 (默认20, 最大100)

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "获取成功",
  "data": {
    "projects": [
      {
        "projectId": 12345,
        "name": "示例项目",
        "description": "项目描述",
        "projectType": "JAVA",
        "status": "AVAILABLE",
        "linesOfCode": 10000,
        "contributorCount": 5,
        "lastAnalyzedAt": "2023-11-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 2. 任务管理 API

#### 2.1 获取任务详情
```http
GET /api/v1/tasks/{taskId}
```

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "获取成功",
  "data": {
    "taskId": "task-uuid-123",
    "projectId": 12345,
    "taskType": "PROJECT_ANALYSIS",
    "status": "PROCESSING",
    "progress": 65,
    "createdAt": "2023-11-01T10:00:00Z",
    "processingStartedAt": "2023-11-01T10:05:00Z",
    "estimatedCompletionAt": "2023-11-01T11:00:00Z",
    "logs": [
      {
        "timestamp": "2023-11-01T10:05:00Z",
        "level": "INFO",
        "message": "开始代码解析"
      }
    ]
  }
}
```

#### 2.2 获取项目相关任务列表
```http
GET /api/v1/projects/{projectId}/tasks
```

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "获取成功",
  "data": {
    "tasks": [
      {
        "taskId": "task-uuid-123",
        "taskType": "PROJECT_ANALYSIS",
        "status": "COMPLETED",
        "progress": 100,
        "createdAt": "2023-11-01T10:00:00Z",
        "completedAt": "2023-11-01T11:00:00Z"
      }
    ]
  }
}
```

### 3. 文档访问 API

#### 3.1 获取项目文档列表
```http
GET /api/v1/projects/{projectId}/documents
```

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "获取成功",
  "data": {
    "documents": [
      {
        "documentType": "PRODUCT_INTRO",
        "title": "产品简介",
        "url": "/docs/{projectId}/intro",
        "lastUpdated": "2023-11-01T12:00:00Z"
      },
      {
        "documentType": "API",
        "title": "接口文档",
        "url": "/docs/{projectId}/api",
        "lastUpdated": "2023-11-01T12:00:00Z"
      }
    ]
  }
}
```

#### 3.2 获取具体文档内容
```http
GET /api/v1/projects/{projectId}/documents/{documentType}
```

**响应**:
```json
{
  "success": true,
  "code": "000000",
  "message": "获取成功",
  "data": {
    "documentType": "API",
    "title": "接口文档",
    "content": "# API 文档\n\n...",
    "format": "markdown",
    "lastUpdated": "2023-11-01T12:00:00Z"
  }
}
```

## 错误码规范

### 系统级错误码 (5位数字)
- `10001`: 请求参数错误
- `10002`: 请求体格式错误
- `10003`: 必填参数缺失
- `20001`: 资源不存在
- `20002`: 资源访问被拒绝
- `30001`: 服务内部错误
- `30002`: 外部服务调用失败

### 业务级错误码 (6位数字)
- `100001`: 项目URL格式错误
- `100002`: 项目已存在
- `100003`: 项目状态不允许此操作
- `200001`: 任务不存在
- `200002`: 任务已完成，无法取消
- `300001`: 文档生成失败
- `300002`: 文档类型不支持

## 接口版本管理

### 版本策略
- 使用URL路径版本控制 (`/api/v1/`, `/api/v2/`)
- 向后兼容原则
- 废弃接口提前通知

### 版本升级
- 新版本发布时保持旧版本可用
- 通过响应头提供版本信息
- 文档同步更新

## 安全考虑

### 输入验证
- 严格的参数类型检查
- SQL注入防护
- XSS攻击防护

### 访问控制
- 基于角色的权限控制
- API访问频率限制
- 敏感操作审计日志

### 数据保护
- 敏感信息脱敏
- 传输加密 (HTTPS)
- 数据备份和恢复

---

**相关文档**:
- [API路径映射](api-path-mapping.md)
- [OpenAPI规范](api-spec.yaml)
- [前端架构设计](frontend-architecture.md)
