package com.archscope.infrastructure.repository;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.cache.RedisService;
import com.archscope.infrastructure.persistence.entity.CapabilityDO;
import com.archscope.infrastructure.persistence.mapper.CapabilityMapper;
import com.archscope.infrastructure.repository.converter.CapabilityConverter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 能力仓储实现类 - 带性能优化
 */
@Repository
public class CapabilityRepositoryImpl implements CapabilityRepository {

    private static final String CACHE_PREFIX_CAPABILITY = "capability:";
    private static final String CACHE_PREFIX_CAPABILITIES_BY_SERVICE = "capabilities:service:";
    private static final String CACHE_PREFIX_CAPABILITIES_BY_NAME = "capabilities:name:";
    private static final String CACHE_PREFIX_CAPABILITIES_BY_TAG = "capabilities:tag:";
    private static final String CACHE_PREFIX_SERVICE_IDS_BY_CAPABILITY = "serviceids:capability:";
    private static final int CACHE_EXPIRE_MINUTES = 30;

    private final CapabilityMapper capabilityMapper;
    private final CapabilityConverter capabilityConverter;
    private final RedisService redisService;

    public CapabilityRepositoryImpl(CapabilityMapper capabilityMapper,
                                   CapabilityConverter capabilityConverter,
                                   RedisService redisService) {
        this.capabilityMapper = capabilityMapper;
        this.capabilityConverter = capabilityConverter;
        this.redisService = redisService;
    }

    @Override
    @Transactional
    public Capability save(Capability capability) {
        CapabilityDO capabilityDO = capabilityConverter.toDataObject(capability);
        
        // 对于新创建的能力，ID为null，直接插入
        // 对于已存在的能力，需要更新
        if (capability.getId() != null && capability.getId().getLongValue() != null) {
            capabilityMapper.updateById(capabilityDO);
        } else {
            capabilityMapper.insert(capabilityDO);
        }
        
        // 清除相关缓存
        clearCapabilityCaches(capability.getId().getValue(), capability.getServiceId().getValue());
        
        return capabilityConverter.toDomainObject(capabilityDO);
    }

    @Override
    public Capability findById(CapabilityId id) {
        String cacheKey = CACHE_PREFIX_CAPABILITY + id.getValue();
        
        // 尝试从缓存获取
        Capability cachedCapability = (Capability) redisService.get(cacheKey);
        if (cachedCapability != null) {
            return cachedCapability;
        }
        
        // 从数据库查询
        CapabilityDO capabilityDO = capabilityMapper.selectById(id.getValue());
        if (capabilityDO == null) {
            return null;
        }
        
        Capability capability = capabilityConverter.toDomainObject(capabilityDO);
        
        // 缓存结果
        redisService.set(cacheKey, capability, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return capability;
    }

    @Override
    public Capability findByServiceIdAndName(ServiceId serviceId, String name) {
        CapabilityDO capabilityDO = capabilityMapper.findByServiceIdAndName(serviceId.getLongValue(), name);
        return capabilityDO != null ? capabilityConverter.toDomainObject(capabilityDO) : null;
    }

    @Override
    public List<Capability> findByServiceId(ServiceId serviceId) {
        String cacheKey = CACHE_PREFIX_CAPABILITIES_BY_SERVICE + serviceId.getValue();
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Capability> cachedCapabilities = redisService.getList(cacheKey, Capability.class);
        if (cachedCapabilities != null) {
            return cachedCapabilities;
        }
        
        List<CapabilityDO> capabilityDOs = capabilityMapper.findByServiceId(serviceId.getValue());
        List<Capability> capabilities = capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, capabilities, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return capabilities;
    }

    @Override
    public List<Capability> findByName(String name) {
        String cacheKey = CACHE_PREFIX_CAPABILITIES_BY_NAME + name;
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Capability> cachedCapabilities = redisService.getList(cacheKey, Capability.class);
        if (cachedCapabilities != null) {
            return cachedCapabilities;
        }
        
        List<CapabilityDO> capabilityDOs = capabilityMapper.findByName(name);
        List<Capability> capabilities = capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, capabilities, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return capabilities;
    }

    public List<Capability> findByTag(String tag) {
        String cacheKey = CACHE_PREFIX_CAPABILITIES_BY_TAG + tag;
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Capability> cachedCapabilities = redisService.getList(cacheKey, Capability.class);
        if (cachedCapabilities != null) {
            return cachedCapabilities;
        }
        
        List<CapabilityDO> capabilityDOs = capabilityMapper.findByTag(tag);
        List<Capability> capabilities = capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, capabilities, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return capabilities;
    }

    @Override
    public List<String> findAllCapabilityNames() {
        String cacheKey = "capability:names:all";
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<String> cachedNames = redisService.getList(cacheKey, String.class);
        if (cachedNames != null) {
            return cachedNames;
        }
        
        List<String> names = capabilityMapper.findAllCapabilityNames();
        
        // 缓存结果
        redisService.setList(cacheKey, names, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return names;
    }

    public List<ServiceId> findServiceIdsByCapabilityName(String capabilityName) {
        String cacheKey = CACHE_PREFIX_SERVICE_IDS_BY_CAPABILITY + capabilityName;
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<ServiceId> cachedServiceIds = redisService.getList(cacheKey, ServiceId.class);
        if (cachedServiceIds != null) {
            return cachedServiceIds;
        }
        
        List<Long> serviceIdLongs = capabilityMapper.findServiceIdsByCapabilityName(capabilityName);
        List<ServiceId> serviceIds = serviceIdLongs.stream()
                .map(ServiceId::of)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, serviceIds, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return serviceIds;
    }

    @Override
    public List<ServiceId> findServiceIdsByCapabilityNames(List<String> capabilityNames) {
        return capabilityNames.stream()
                .flatMap(name -> findServiceIdsByCapabilityName(name).stream())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Capability> findWithPagination(int offset, int limit) {
        List<CapabilityDO> capabilityDOs = capabilityMapper.findWithPagination(offset, limit);
        return capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Capability> findAll() {
        List<CapabilityDO> capabilityDOs = capabilityMapper.selectList(null);
        return capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean delete(CapabilityId id) {
        // 先获取能力信息用于清除缓存
        CapabilityDO capabilityDO = capabilityMapper.selectById(id.getValue());
        if (capabilityDO == null) {
            return false;
        }
        
        int deletedRows = capabilityMapper.deleteById(id.getValue());
        if (deletedRows > 0) {
            // 清除缓存时不需要serviceId，因为我们已经有了capabilityId
            clearCapabilityCaches(id.getValue(), null);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean deleteByServiceId(ServiceId serviceId) {
        int deletedRows = capabilityMapper.deleteByServiceId(serviceId.getLongValue());
        if (deletedRows > 0) {
            clearServiceCapabilityCaches(serviceId.getValue());
            return true;
        }
        return false;
    }

    @Override
    public long count() {
        return capabilityMapper.selectCount(null);
    }

    @Override
    public List<Capability> searchByDescription(String descriptionLike) {
        List<CapabilityDO> capabilityDOs = capabilityMapper.findByDescriptionLike(descriptionLike);
        return capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Capability> findRecentlyAdded(int limit) {
        List<CapabilityDO> capabilityDOs = capabilityMapper.findRecentlyAdded(limit);
        return capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Capability> findRecentlyUpdated(int limit) {
        List<CapabilityDO> capabilityDOs = capabilityMapper.findRecentlyUpdated(limit);
        return capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    /**
     * 批量保存能力
     */
    @Transactional
    public List<Capability> saveBatch(List<Capability> capabilities) {
        List<CapabilityDO> capabilityDOs = capabilities.stream()
                .map(capabilityConverter::toDataObject)
                .collect(Collectors.toList());
        
        // 使用MyBatis Plus的批量操作
        for (CapabilityDO capabilityDO : capabilityDOs) {
            // 对于批量操作，直接插入新记录
            capabilityMapper.insert(capabilityDO);
        }
        
        // 清除相关缓存
        capabilities.forEach(capability -> 
            clearCapabilityCaches(capability.getId().getValue(), capability.getServiceId().getValue()));
        
        return capabilities.stream()
                .map(capability -> capabilityConverter.toDomainObject(capabilityConverter.toDataObject(capability)))
                .collect(Collectors.toList());
    }

    /**
     * 批量删除能力
     */
    @Transactional
    public boolean deleteBatch(List<CapabilityId> capabilityIds) {
        List<String> ids = capabilityIds.stream()
                .map(CapabilityId::getValue)
                .collect(Collectors.toList());
        
        // 先获取能力信息用于清除缓存
        List<CapabilityDO> capabilityDOs = capabilityMapper.selectBatchIds(ids);
        
        int deletedRows = capabilityMapper.deleteBatchIds(ids);
        
        if (deletedRows > 0) {
            // 批量删除后清除所有相关缓存
            // 由于我们不能访问具体的ID，清除所有能力相关的缓存
            capabilityDOs.forEach(capabilityDO ->
                clearCapabilityCaches(null, null));
            return true;
        }
        return false;
    }

    /**
     * 批量查询能力（通过服务ID列表）
     */
    public List<Capability> findByServiceIds(List<ServiceId> serviceIds) {
        List<String> serviceIdStrings = serviceIds.stream()
                .map(ServiceId::getValue)
                .collect(Collectors.toList());
        
        List<CapabilityDO> capabilityDOs = capabilityMapper.findByServiceIds(serviceIdStrings);
        return capabilityDOs.stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    /**
     * 优化的复合查询方法
     */
    public List<Capability> findByMultipleCriteria(String serviceId, String name, String description,
                                                  String tag, String orderBy, int offset, int limit) {
        // 构建缓存键
        String cacheKey = buildComplexQueryCacheKey(serviceId, name, description, tag, orderBy, offset, limit);
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Capability> cachedCapabilities = redisService.getList(cacheKey, Capability.class);
        if (cachedCapabilities != null) {
            return cachedCapabilities;
        }
        
        // 构建查询条件
        QueryWrapper<CapabilityDO> queryWrapper = new QueryWrapper<>();
        
        if (serviceId != null && !serviceId.isEmpty()) {
            queryWrapper.eq("service_id", serviceId);
        }
        if (name != null && !name.isEmpty()) {
            queryWrapper.like("name", name);
        }
        if (description != null && !description.isEmpty()) {
            queryWrapper.like("description", description);
        }
        
        // 排序
        if (orderBy != null && !orderBy.isEmpty()) {
            queryWrapper.orderByDesc(orderBy);
        } else {
            queryWrapper.orderByDesc("created_at");
        }
        
        // 分页
        Page<CapabilityDO> page = new Page<>(offset / limit + 1, limit);
        Page<CapabilityDO> result = capabilityMapper.selectPage(page, queryWrapper);
        
        List<Capability> capabilities = result.getRecords().stream()
                .map(capabilityConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果（较短的过期时间）
        redisService.setList(cacheKey, capabilities, 10, TimeUnit.MINUTES);
        
        return capabilities;
    }

    private void clearCapabilityCaches(String capabilityId, String serviceId) {
        // 清除单个能力缓存
        redisService.delete(CACHE_PREFIX_CAPABILITY + capabilityId);
        
        // 清除服务相关的能力缓存
        redisService.delete(CACHE_PREFIX_CAPABILITIES_BY_SERVICE + serviceId);
        
        // 清除相关的列表缓存
        redisService.deleteByPattern(CACHE_PREFIX_CAPABILITIES_BY_NAME + "*");
        redisService.deleteByPattern(CACHE_PREFIX_CAPABILITIES_BY_TAG + "*");
        redisService.deleteByPattern(CACHE_PREFIX_SERVICE_IDS_BY_CAPABILITY + "*");
        redisService.delete("capability:names:all");
    }

    private void clearServiceCapabilityCaches(String serviceId) {
        // 清除服务相关的能力缓存
        redisService.delete(CACHE_PREFIX_CAPABILITIES_BY_SERVICE + serviceId);
        
        // 清除相关的列表缓存
        redisService.deleteByPattern(CACHE_PREFIX_CAPABILITIES_BY_NAME + "*");
        redisService.deleteByPattern(CACHE_PREFIX_CAPABILITIES_BY_TAG + "*");
        redisService.deleteByPattern(CACHE_PREFIX_SERVICE_IDS_BY_CAPABILITY + "*");
        redisService.delete("capability:names:all");
    }

    private String buildComplexQueryCacheKey(String serviceId, String name, String description,
                                           String tag, String orderBy, int offset, int limit) {
        return "capabilities:complex:" + 
               (serviceId != null ? serviceId : "") + ":" +
               (name != null ? name : "") + ":" +
               (description != null ? description : "") + ":" +
               (tag != null ? tag : "") + ":" +
               (orderBy != null ? orderBy : "") + ":" +
               offset + ":" + limit;
    }
}