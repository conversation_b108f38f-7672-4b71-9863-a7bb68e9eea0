package com.archscope.app.controller;

import com.archscope.app.command.CapabilityRegistrationCommand;
import com.archscope.app.dto.CapabilityDTO;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.service.CapabilityManagementService;
import com.archscope.facade.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 能力管理控制器
 * 提供服务能力注册、查询和管理的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/capabilities")
@RequiredArgsConstructor
@Validated
public class CapabilityController {

    private final CapabilityManagementService capabilityManagementService;

    /**
     * 为服务注册能力
     *
     * @param serviceId 服务ID
     * @param command 能力注册命令
     * @return 注册成功的能力信息
     */
    @PostMapping("/services/{serviceId}")
    public ResponseEntity<ApiResponse<CapabilityDTO>> registerCapability(
            @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId,
            @Valid @RequestBody CapabilityRegistrationCommand command) {
        log.info("为服务注册能力请求: serviceId={}, capabilityName={}", serviceId, command.getName());
        CapabilityDTO capabilityDTO = capabilityManagementService.registerCapability(serviceId, command);
        log.info("能力注册成功，能力ID: {}", capabilityDTO.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("能力注册成功", capabilityDTO));
    }

    /**
     * 获取服务的所有能力
     *
     * @param serviceId 服务ID
     * @return 服务能力列表
     */
    @GetMapping("/services/{serviceId}")
    public ResponseEntity<ApiResponse<List<CapabilityDTO>>> getServiceCapabilities(
            @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        log.info("获取服务能力请求: serviceId={}", serviceId);
        List<CapabilityDTO> capabilities = capabilityManagementService.getServiceCapabilities(serviceId);
        log.info("获取到{}个服务能力", capabilities.size());
        return ResponseEntity.ok(ApiResponse.success("获取服务能力成功", capabilities));
    }

    /**
     * 根据能力需求查找提供相关能力的服务
     *
     * @param requiredCapabilities 需要的能力名称列表
     * @return 提供这些能力的服务列表
     */
    @GetMapping("/search/services")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByCapabilityRequirements(
            @RequestParam @NotEmpty(message = "能力需求列表不能为空") List<String> requiredCapabilities) {
        log.info("根据能力需求查找服务请求: requiredCapabilities={}", requiredCapabilities);
        List<ServiceDTO> services = capabilityManagementService.findServicesByCapabilityRequirements(requiredCapabilities);
        log.info("根据能力需求找到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据能力需求查找服务成功", services));
    }

    /**
     * 根据能力ID获取能力详情
     *
     * @param capabilityId 能力ID
     * @return 能力详情
     */
    @GetMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<CapabilityDTO>> getCapabilityById(
            @PathVariable @NotBlank(message = "能力ID不能为空") String capabilityId) {
        log.info("获取能力详情请求: capabilityId={}", capabilityId);
        CapabilityDTO capability = capabilityManagementService.getCapabilityById(capabilityId);
        return ResponseEntity.ok(ApiResponse.success("获取能力详情成功", capability));
    }

    /**
     * 更新能力信息
     *
     * @param capabilityId 能力ID
     * @param command 能力注册命令（用于更新）
     * @return 更新后的能力信息
     */
    @PutMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<CapabilityDTO>> updateCapability(
            @PathVariable @NotBlank(message = "能力ID不能为空") String capabilityId,
            @Valid @RequestBody CapabilityRegistrationCommand command) {
        log.info("更新能力请求: capabilityId={}", capabilityId);
        CapabilityDTO capability = capabilityManagementService.updateCapability(capabilityId, command);
        log.info("能力更新成功: capabilityId={}", capabilityId);
        return ResponseEntity.ok(ApiResponse.success("能力更新成功", capability));
    }

    /**
     * 标记能力为废弃状态
     *
     * @param capabilityId 能力ID
     * @return 操作结果
     */
    @PatchMapping("/{capabilityId}/deprecate")
    public ResponseEntity<ApiResponse<Void>> deprecateCapability(
            @PathVariable @NotBlank(message = "能力ID不能为空") String capabilityId) {
        log.info("废弃能力请求: capabilityId={}", capabilityId);
        capabilityManagementService.deprecateCapability(capabilityId);
        log.info("能力废弃成功: capabilityId={}", capabilityId);
        return ResponseEntity.ok(ApiResponse.success("能力废弃成功", null));
    }

    /**
     * 删除能力
     *
     * @param capabilityId 能力ID
     * @return 操作结果
     */
    @DeleteMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<Void>> deleteCapability(
            @PathVariable @NotBlank(message = "能力ID不能为空") String capabilityId) {
        log.info("删除能力请求: capabilityId={}", capabilityId);
        capabilityManagementService.deleteCapability(capabilityId);
        log.info("能力删除成功: capabilityId={}", capabilityId);
        return ResponseEntity.ok(ApiResponse.success("能力删除成功", null));
    }
}